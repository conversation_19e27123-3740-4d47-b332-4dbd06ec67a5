#!/bin/bash

# Create necessary directories
mkdir -p /tmp/loki/chunks
mkdir -p /tmp/loki/rules
mkdir -p logs

# Download Loki if not exists
if [ ! -f "loki" ]; then
    echo "Downloading Loki..."
    curl -O -L "https://github.com/grafana/loki/releases/download/v2.9.0/loki-linux-amd64.zip"
    unzip loki-linux-amd64.zip
    chmod +x loki-linux-amd64
    mv loki-linux-amd64 loki
    rm loki-linux-amd64.zip
fi

# Download Promtail if not exists
if [ ! -f "promtail" ]; then
    echo "Downloading Promtail..."
    curl -O -L "https://github.com/grafana/loki/releases/download/v2.9.0/promtail-linux-amd64.zip"
    unzip promtail-linux-amd64.zip
    chmod +x promtail-linux-amd64
    mv promtail-linux-amd64 promtail
    rm promtail-linux-amd64.zip
fi

echo "Setup complete!"
echo "To start Loki: ./loki -config.file=loki-config.yaml"
echo "To start Promtail: ./promtail -config.file=promtail-config.yaml"
