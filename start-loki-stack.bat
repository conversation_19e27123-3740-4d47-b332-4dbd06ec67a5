@echo off
echo Starting <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> with Docker Compose...
echo This will start:
echo - Loki on http://localhost:3100
echo - Grafana on http://localhost:3000 (admin/admin)
echo - Promtail (log collector)
echo.

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Create a sample log file for testing
echo %date% %time%: Sample log entry for testing >> logs\app.log

echo Starting services...
docker-compose up -d

echo.
echo Services started! You can access:
echo - Grafana: http://localhost:3000 (username: admin, password: admin)
echo - Loki API: http://localhost:3100
echo.
echo To view logs: docker-compose logs -f
echo To stop services: docker-compose down
pause
