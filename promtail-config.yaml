server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*log

  - job_name: containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: containerlogs
          __path__: /var/lib/docker/containers/*/*log

  # For Windows logs (if running on Windows)
  - job_name: windows-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: windowslogs
          __path__: C:/Windows/System32/winevt/Logs/*.evtx

  # Custom application logs
  - job_name: app-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: applogs
          __path__: ./logs/*.log
