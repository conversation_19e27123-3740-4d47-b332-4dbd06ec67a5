#!/bin/bash

# Create logs directory
mkdir -p logs

echo "Generating sample logs..."

# Generate different types of logs
while true; do
    # Application logs
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] Application started successfully" >> logs/app.log
    sleep 2
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') [DEBUG] Processing user request ID: $(shuf -i 1000-9999 -n 1)" >> logs/app.log
    sleep 1
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARN] High memory usage detected: $(shuf -i 70-95 -n 1)%" >> logs/app.log
    sleep 3
    
    # Error logs occasionally
    if [ $((RANDOM % 10)) -eq 0 ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] Database connection failed - retrying..." >> logs/app.log
    fi
    
    # Access logs
    echo "$(date '+%Y-%m-%d %H:%M:%S') 192.168.1.$(shuf -i 1-254 -n 1) GET /api/users/$(shuf -i 1-1000 -n 1) 200 $(shuf -i 100-500 -n 1)ms" >> logs/access.log
    
    sleep 2
done
