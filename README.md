# Grafana Loki with Promtail Setup

This repository contains the configuration and scripts to run Grafana Loki with <PERSON><PERSON><PERSON> for log aggregation and monitoring.

## What's Included

- **Loki**: Log aggregation system
- **Promtail**: Log collector that ships logs to Loki
- **<PERSON>ana**: Visualization and dashboarding (optional)

## Files Overview

- `loki-config.yaml`: Loki server configuration
- `promtail-config.yaml`: Promtail configuration for log collection
- `docker-compose.yml`: Docker Compose setup for easy deployment
- `start-loki-stack.sh`: Script to start all services with Docker
- `generate-logs.sh`: Script to generate sample logs for testing

## Setup Options

### Option 1: Docker Compose (Recommended)

1. **Start Docker Desktop** (if on Windows/Mac)
2. Run the setup:
   ```bash
   ./start-loki-stack.sh
   ```

This will start:
- Loki on http://localhost:3100
- Grafana on http://localhost:3000 (admin/admin)
- Promtail (log collector)

### Option 2: Manual Binary Setup

If you prefer to run binaries directly:

1. Download Windows binaries:
   ```bash
   # Download Loki for Windows
   curl -O -L "https://github.com/grafana/loki/releases/download/v2.9.0/loki-windows-amd64.exe.zip"
   unzip loki-windows-amd64.exe.zip
   
   # Download Promtail for Windows
   curl -O -L "https://github.com/grafana/loki/releases/download/v2.9.0/promtail-windows-amd64.exe.zip"
   unzip promtail-windows-amd64.exe.zip
   ```

2. Start Loki:
   ```bash
   ./loki-windows-amd64.exe -config.file=loki-config.yaml
   ```

3. Start Promtail (in another terminal):
   ```bash
   ./promtail-windows-amd64.exe -config.file=promtail-config.yaml
   ```

## Testing the Setup

1. **Generate sample logs**:
   ```bash
   ./generate-logs.sh
   ```

2. **Access Grafana**:
   - URL: http://localhost:3000
   - Username: admin
   - Password: admin

3. **Add Loki as Data Source in Grafana**:
   - Go to Configuration > Data Sources
   - Add Loki data source
   - URL: http://loki:3100 (if using Docker) or http://localhost:3100 (if running locally)

4. **Query logs**:
   - Go to Explore in Grafana
   - Use LogQL queries like: `{job="applogs"}`

## Log Collection

Promtail is configured to collect logs from:
- `/var/log/*log` (system logs)
- `./logs/*.log` (application logs in the logs directory)
- Docker container logs (if available)

## Useful Commands

```bash
# View Docker logs
docker-compose logs -f

# Stop services
docker-compose down

# Restart services
docker-compose restart

# View Loki metrics
curl http://localhost:3100/metrics
```

## Configuration Customization

- Edit `loki-config.yaml` to modify Loki settings
- Edit `promtail-config.yaml` to add more log sources
- Edit `docker-compose.yml` to modify service configurations

## Troubleshooting

1. **Docker issues**: Make sure Docker Desktop is running
2. **Port conflicts**: Check if ports 3000, 3100, 9080 are available
3. **Log permissions**: Ensure Promtail has read access to log files
4. **Windows paths**: Update log paths in promtail-config.yaml for Windows-specific locations
