#!/bin/bash

echo "Starting <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> with <PERSON>er Compose..."
echo "This will start:"
echo "- <PERSON> on http://localhost:3100"
echo "- <PERSON><PERSON> on http://localhost:3000 (admin/admin)"
echo "- Promtail (log collector)"
echo ""

# Create logs directory if it doesn't exist
mkdir -p logs

# Create a sample log file for testing
echo "$(date): Sample log entry for testing" >> logs/app.log

echo "Starting services..."
docker-compose up -d

echo ""
echo "Services started! You can access:"
echo "- <PERSON>ana: http://localhost:3000 (username: admin, password: admin)"
echo "- Loki API: http://localhost:3100"
echo ""
echo "To view logs: docker-compose logs -f"
echo "To stop services: docker-compose down"
