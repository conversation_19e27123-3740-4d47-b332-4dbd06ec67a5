Logging - Metrics & User Behavior (<PERSON> with Prom<PERSON> & <PERSON><PERSON>)

https://youtu.be/0B-yQdSXFJE
https://github.com/grafana/loki?tab=readme-ov-file
https://youtu.be/O52dseg2bJo?si=06T94nDKXvSpYrPv



docker Setup

curl.exe -L -o loki-linux-amd64.zip "https://github.com/grafana/loki/releases/download/v2.8.2/loki-linux-amd64.zip"

Stop the servers:
# Stop the log generator
docker-compose down

# Or if you want to stop and remove volumes (clean restart)
docker-compose down -v

Restart the servers:
# Start all services again
docker-compose up -d

# Start the log generator (optional)
./generate-logs.sh

Check status:
# View running containers
docker-compose ps

# View logs
docker-compose logs -f


HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ls -la loki-linux-amd64/
total 115184
drwxr-xr-x 1 <USER> <GROUP>         0 Aug 20 17:46 ./
drwxr-xr-x 1 <USER> <GROUP>         0 Aug 20 17:56 ../
-rw-r--r-- 1 <USER> <GROUP> 117944504 Jul 16 21:58 loki-linux-amd64

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ dir
loki  loki-linux-amd64  test.txt

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ls -la
total 4
drwxr-xr-x 1 <USER> <GROUP> 0 Aug 20 17:56 ./
drwxr-xr-x 1 <USER> <GROUP> 0 Aug 20 15:53 ../
drwxr-xr-x 1 <USER> <GROUP> 0 Aug 20 17:56 loki/
drwxr-xr-x 1 <USER> <GROUP> 0 Aug 20 17:46 loki-linux-amd64/
-rw-r--r-- 1 <USER> <GROUP> 0 Aug 20 15:54 test.txt

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ls -la loki-linux-amd64/
total 115184
drwxr-xr-x 1 <USER> <GROUP>         0 Aug 20 17:46 ./
drwxr-xr-x 1 <USER> <GROUP>         0 Aug 20 17:56 ../
-rw-r--r-- 1 <USER> <GROUP> 117944504 Jul 16 21:58 loki-linux-amd64

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ chmod +x setup-loki.sh start-loki.sh start-promtail.sh

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ./setup-loki.sh
Downloading Loki...
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100 19.7M  100 19.7M    0     0  4243k      0  0:00:04  0:00:04 --:--:-- 4861k
Archive:  loki-linux-amd64.zip
replace loki-linux-amd64? [y]es, [n]o, [A]ll, [N]one, [r]ename: y
error:  cannot delete old loki-linux-amd64
        Is a directory
Downloading Promtail...
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100 25.7M  100 25.7M    0     0  4957k      0  0:00:05  0:00:05 --:--:-- 5996k
Archive:  promtail-linux-amd64.zip
  inflating: promtail-linux-amd64
Setup complete!
To start Loki: ./loki -config.file=loki-config.yaml
To start Promtail: ./promtail -config.file=promtail-config.yaml

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ rm -rf loki-linux-amd64

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ unzip -o loki-linux-amd64.zip
unzip:  cannot find or open loki-linux-amd64.zip, loki-linux-amd64.zip.zip or loki-linux-amd64.zip.ZIP.

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ls -la
total 91602
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 18:02 ./
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 15:53 ../
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 18:02 logs/
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 18:02 loki/
-rw-r--r-- 1 <USER> <GROUP>     1296 Aug 20 18:01 loki-config.yaml
-rw-r--r-- 1 <USER> <GROUP> 93775016 Sep  7  2023 promtail
-rw-r--r-- 1 <USER> <GROUP>      930 Aug 20 18:01 promtail-config.yaml
-rwxr-xr-x 1 <USER> <GROUP>      905 Aug 20 18:01 setup-loki.sh*
-rwxr-xr-x 1 <USER> <GROUP>       74 Aug 20 18:01 start-loki.sh*
-rwxr-xr-x 1 <USER> <GROUP>       86 Aug 20 18:01 start-promtail.sh*
-rw-r--r-- 1 <USER> <GROUP>        0 Aug 20 15:54 test.txt

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ls -la loki/
total 35808
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 18:02 ./
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 18:02 ../
drwxr-xr-x 1 <USER> <GROUP>        0 Aug 20 17:46 loki-linux-amd64/
-rw-r--r-- 1 <USER> <GROUP> 36662560 Aug 20 17:43 loki-linux-amd64.zip

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ find . -name "*loki*" -type f
./loki/loki-linux-amd64/loki-linux-amd64
./loki/loki-linux-amd64.zip
./loki-config.yaml
./setup-loki.sh
./start-loki.sh

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ cp loki/loki-linux-amd64/loki-linux-amd64 ./loki
cp: cannot overwrite directory './loki/loki-linux-amd64' with non-directory

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ cp "loki/loki-linux-amd64/loki-linux-amd64" ./loki-binary

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ chmod +x loki-binary promtail

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ mkdir -p /tmp/loki/chunks /tmp/loki/rules

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ uname -a
MINGW64_NT-10.0-22631 LAPTOP-LC7QCH3M 3.5.7-882031da.x86_64 2025-01-30 11:14 UTC x86_64 Msys

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ echo $OS
Windows_NT

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ chmod +x start-loki-stack.sh

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ chmod +x generate-logs.sh

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ./start-loki-stack.sh
Starting Loki, Promtail, and Grafana with Docker Compose...
This will start:
- Loki on http://localhost:3100
- Grafana on http://localhost:3000 (admin/admin)
- Promtail (log collector)

Starting services...
time="2025-08-20T18:05:35+05:30" level=warning msg="D:\\React\\react latest knowledge fil\\docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
unable to get image 'grafana/promtail:2.9.0': error during connect: Get "http://%2F%2F.%2Fpipe%2FdockerDesktopLinuxEngine/v1.49/images/grafana/promtail:2.9.0/json": open //./pipe/dockerDesktopLinuxEngine: The system cannot find the file specified.

Services started! You can access:
- Grafana: http://localhost:3000 (username: admin, password: admin)
- Loki API: http://localhost:3100

To view logs: docker-compose logs -f
To stop services: docker-compose down

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ docker --version
Docker version 28.1.1, build 4eba377

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ docker-compose up -d
time="2025-08-20T18:08:35+05:30" level=warning msg="D:\\React\\react latest knowledge fil\\docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
[+] Running 25/25
 ✔ grafana Pulled                                                                                                                     59.4s 
 ✔ loki Pulled                                                                                                                        24.0s 
 ✔ promtail Pulled                                                                                                                    39.9s 






[+] Running 7/7
 ✔ Network reactlatestknowledgefil_loki            Created                                                                             0.0s 
 ✔ Volume "reactlatestknowledgefil_loki-data"      Created                                                                             0.0s 
 ✔ Volume "reactlatestknowledgefil_promtail-data"  Created                                                                             0.0s 
 ✔ Volume "reactlatestknowledgefil_grafana-data"   Created                                                                             0.0s 
 ✔ Container reactlatestknowledgefil-loki-1        Started                                                                             1.1s 
 ✔ Container reactlatestknowledgefil-grafana-1     Started                                                                             1.2s 
 ✔ Container reactlatestknowledgefil-promtail-1    Started                                                                             1.1s 

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ docker-compose ps
time="2025-08-20T18:09:48+05:30" level=warning msg="D:\\React\\react latest knowledge fil\\docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
NAME                                 IMAGE                    COMMAND                  SERVICE    CREATED          STATUS          PORTS
reactlatestknowledgefil-grafana-1    grafana/grafana:latest   "/run.sh"                grafana    13 seconds ago   Up 11 seconds   0.0.0.0:3000->3000/tcp
reactlatestknowledgefil-loki-1       grafana/loki:2.9.0       "/usr/bin/loki -conf…"   loki       13 seconds ago   Up 12 seconds   0.0.0.0:3100->3100/tcp
reactlatestknowledgefil-promtail-1   grafana/promtail:2.9.0   "/usr/bin/promtail -…"   promtail   13 seconds ago   Up 11 seconds

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ls -la logs/
total 6
drwxr-xr-x 1 <USER> <GROUP>   0 Aug 20 18:10 ./
drwxr-xr-x 1 <USER> <GROUP>   0 Aug 20 18:06 ../
-rw-r--r-- 1 <USER> <GROUP>  63 Aug 20 18:10 access.log
-rw-r--r-- 1 <USER> <GROUP> 300 Aug 20 18:10 app.log

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ tail -f logs/access.log
2025-08-20 18:10:25 ************* GET /api/users/569 200 363ms
2025-08-20 18:10:33 ************ GET /api/users/620 200 323ms
2025-08-20 18:10:42 ************ GET /api/users/4 200 332ms


HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ curl -s "http://localhost:3100/loki/api/v1/labels"
{"status":"success"}

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ curl -s "http://localhost:3100/ready"
Ingester not ready: waiting for 15s after being ready

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ docker-compose logs --tail=10 loki
time="2025-08-20T18:11:11+05:30" level=warning msg="D:\\React\\react latest knowledge fil\\docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
loki-1  | level=info ts=2025-08-20T12:40:36.59188488Z caller=table_manager.go:171 index-store=boltdb-shipper-2020-10-24 msg="handing over indexes to shipper"
loki-1  | level=info ts=2025-08-20T12:40:53.79550658Z caller=roundtrip.go:295 org_id=fake traceID=2128aded63b022f3 msg="executing query" type=labels label= length=1h0m0s query=
loki-1  | level=info ts=2025-08-20T12:40:53.799616209Z caller=table_manager.go:195 index-store=boltdb-shipper-2020-10-24 msg="downloading all files for table index_20320"
loki-1  | ts=2025-08-20T12:40:53.800162673Z caller=spanlogger.go:86 table-name=index_20320 user-id=fake org_id=fake traceID=2128aded63b022f3 level=info msg="downloaded index set at query time" duration=163.003µs
loki-1  | ts=2025-08-20T12:40:53.800237527Z caller=spanlogger.go:86 table-name=index_20320 org_id=fake traceID=2128aded63b022f3 level=info msg="downloaded index set at query time" duration=49.852µs
loki-1  | ts=2025-08-20T12:40:53.800774384Z caller=spanlogger.go:86 user=fake level=info org_id=fake traceID=2128aded63b022f3 latency=fast query_type=labels length=1h0m0s duration=3.859097ms status=200 label= query= splits=0 throughput=0B total_bytes=0B total_entries=0
loki-1  | level=info ts=2025-08-20T12:40:53.803826187Z caller=metrics.go:207 component=frontend org_id=fake traceID=2128aded63b022f3 latency=fast query_type=labels length=1h0m0s duration=8.038534ms status=200 label= query= splits=0 throughput=0B total_bytes=0B total_entries=0    
loki-1  | level=info ts=2025-08-20T12:41:00.624436791Z caller=roundtrip.go:295 org_id=fake traceID=3cd5a0dffa1b6dce msg="executing query" type=labels label= length=1h0m0s query=
loki-1  | ts=2025-08-20T12:41:00.627539112Z caller=spanlogger.go:86 user=fake level=info org_id=fake traceID=3cd5a0dffa1b6dce latency=fast query_type=labels length=1h0m0s duration=1.911186ms status=200 label= query= splits=0 throughput=0B total_bytes=0B total_entries=0
loki-1  | level=info ts=2025-08-20T12:41:00.627969537Z caller=metrics.go:207 component=frontend org_id=fake traceID=3cd5a0dffa1b6dce latency=fast query_type=labels length=1h0m0s duration=3.426402ms status=200 label= query= splits=0 throughput=0B total_bytes=0B total_entries=0    

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ docker-compose logs --tail=10 promtail
time="2025-08-20T18:11:19+05:30" level=warning msg="D:\\React\\react latest knowledge fil\\docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
promtail-1  | level=info ts=2025-08-20T12:39:36.889290759Z caller=server.go:322 http=[::]:9080 grpc=[::]:35935 msg="server listening on addresses"
promtail-1  | level=info ts=2025-08-20T12:39:36.88948277Z caller=main.go:174 msg="Starting Promtail" version="(version=2.9.0, branch=HEAD, revision=2feb64f69)"
promtail-1  | level=warn ts=2025-08-20T12:39:36.889552626Z caller=promtail.go:263 msg="enable watchConfig"
promtail-1  | level=info ts=2025-08-20T12:39:41.888581286Z caller=filetargetmanager.go:361 msg="Adding target" key="/var/lib/docker/containers/*/*log:{job=\"containerlogs\"}"
promtail-1  | level=info ts=2025-08-20T12:39:41.888638232Z caller=filetargetmanager.go:361 msg="Adding target" key="C:/Windows/System32/winevt/Logs/*.evtx:{job=\"windowslogs\"}"
promtail-1  | level=info ts=2025-08-20T12:39:41.88865885Z caller=filetargetmanager.go:361 msg="Adding target" key="./logs/*.log:{job=\"applogs\"}"
promtail-1  | level=info ts=2025-08-20T12:39:41.888675783Z caller=filetargetmanager.go:361 msg="Adding target" key="/var/log/*log:{job=\"varlogs\"}"
promtail-1  | level=info ts=2025-08-20T12:39:41.888788471Z caller=filetarget.go:313 msg="watching new directory" directory=/var/log
promtail-1  | ts=2025-08-20T12:39:41.889028145Z caller=log.go:168 level=info msg="Seeked /var/log/lastlog - &{Offset:0 Whence:0}"
promtail-1  | level=info ts=2025-08-20T12:39:41.889063584Z caller=tailer.go:145 component=tailer msg="tail routine: started" path=/var/log/lastlog

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil
$ ^C

HP@LAPTOP-LC7QCH3M MINGW64 /d/React/react latest knowledge fil